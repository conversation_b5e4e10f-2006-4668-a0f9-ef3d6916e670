import React, { useState, useEffect } from 'react'
import { Wand2, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Settings } from 'lucide-react'
import axios from 'axios'

interface FilterPanelProps {
  onRemoveBackground: (backgroundType: string) => void
  onCharacterConversion: (style: string) => void
  onApplyFilter: (filterName: string, intensity: number) => void
  isProcessing: boolean
}

interface Filter {
  name: string
  display_name: string
  description: string
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  onRemoveBackground,
  onCharacterConversion,
  onApplyFilter,
  isProcessing
}) => {
  const [activeTab, setActiveTab] = useState<'background' | 'character' | 'filters'>('background')
  const [availableFilters, setAvailableFilters] = useState<Filter[]>([])
  const [filterIntensity, setFilterIntensity] = useState(1.0)

  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

  useEffect(() => {
    // جلب قائمة الفلاتر المتاحة
    const fetchFilters = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/filters`)
        setAvailableFilters(response.data.filters)
      } catch (error) {
        console.error('خطأ في جلب الفلاتر:', error)
      }
    }

    fetchFilters()
  }, [API_BASE_URL])

  const backgroundOptions = [
    { value: 'transparent', label: 'شفاف', icon: '🔳', description: 'خلفية شفافة' },
    { value: 'white', label: 'أبيض', icon: '⬜', description: 'خلفية بيضاء' },
    { value: 'black', label: 'أسود', icon: '⬛', description: 'خلفية سوداء' },
    { value: 'green', label: 'أخضر', icon: '🟢', description: 'شاشة خضراء' },
    { value: 'blue', label: 'أزرق', icon: '🔵', description: 'شاشة زرقاء' },
    { value: 'gradient', label: 'متدرج', icon: '🌈', description: 'خلفية متدرجة' }
  ]

  const characterStyles = [
    { value: 'anime', label: 'أنمي', icon: '🎌', description: 'تحويل إلى شخصية أنمي' },
    { value: 'cartoon', label: 'كرتون', icon: '🎨', description: 'تحويل إلى شخصية كرتونية' },
    { value: 'sketch', label: 'رسم', icon: '✏️', description: 'تحويل إلى رسم بالقلم' },
    { value: 'oil_painting', label: 'لوحة زيتية', icon: '🖼️', description: 'تحويل إلى لوحة زيتية' }
  ]

  const getFilterIcon = (filterName: string) => {
    const icons: { [key: string]: string } = {
      vintage: '📸',
      cyberpunk: '🌆',
      oil_painting: '🎨',
      watercolor: '🌊',
      neon: '💡',
      black_white: '⚫',
      sepia: '🟤',
      blur: '🌫️'
    }
    return icons[filterName] || '✨'
  }

  return (
    <div className="filter-panel">
      <div className="panel-tabs">
        <button
          className={`tab ${activeTab === 'background' ? 'active' : ''}`}
          onClick={() => setActiveTab('background')}
          disabled={isProcessing}
        >
          <Image size={20} />
          إزالة الخلفية
        </button>
        
        <button
          className={`tab ${activeTab === 'character' ? 'active' : ''}`}
          onClick={() => setActiveTab('character')}
          disabled={isProcessing}
        >
          <Wand2 size={20} />
          تحويل الشخصية
        </button>
        
        <button
          className={`tab ${activeTab === 'filters' ? 'active' : ''}`}
          onClick={() => setActiveTab('filters')}
          disabled={isProcessing}
        >
          <Sparkles size={20} />
          الفلاتر
        </button>
      </div>

      <div className="panel-content">
        {activeTab === 'background' && (
          <div className="background-section">
            <h3>🖼️ إزالة الخلفية</h3>
            <p>اختر نوع الخلفية الجديدة:</p>
            
            <div className="options-grid">
              {backgroundOptions.map((option) => (
                <button
                  key={option.value}
                  className="option-card"
                  onClick={() => onRemoveBackground(option.value)}
                  disabled={isProcessing}
                >
                  <div className="option-icon">{option.icon}</div>
                  <div className="option-label">{option.label}</div>
                  <div className="option-description">{option.description}</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'character' && (
          <div className="character-section">
            <h3>🎨 تحويل الشخصية</h3>
            <p>اختر نمط التحويل:</p>
            
            <div className="options-grid">
              {characterStyles.map((style) => (
                <button
                  key={style.value}
                  className="option-card"
                  onClick={() => onCharacterConversion(style.value)}
                  disabled={isProcessing}
                >
                  <div className="option-icon">{style.icon}</div>
                  <div className="option-label">{style.label}</div>
                  <div className="option-description">{style.description}</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'filters' && (
          <div className="filters-section">
            <h3>✨ الفلاتر الاحترافية</h3>
            
            <div className="intensity-control">
              <label>
                <Settings size={16} />
                شدة التأثير: {Math.round(filterIntensity * 100)}%
              </label>
              <input
                type="range"
                min="0.1"
                max="2.0"
                step="0.1"
                value={filterIntensity}
                onChange={(e) => setFilterIntensity(parseFloat(e.target.value))}
                disabled={isProcessing}
              />
            </div>
            
            <div className="options-grid">
              {availableFilters.map((filter) => (
                <button
                  key={filter.name}
                  className="option-card"
                  onClick={() => onApplyFilter(filter.name, filterIntensity)}
                  disabled={isProcessing}
                >
                  <div className="option-icon">{getFilterIcon(filter.name)}</div>
                  <div className="option-label">{filter.display_name}</div>
                  <div className="option-description">{filter.description}</div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {isProcessing && (
        <div className="processing-notice">
          <div className="spinner-small"></div>
          <span>جاري المعالجة... يرجى الانتظار</span>
        </div>
      )}
    </div>
  )
}

export default FilterPanel
