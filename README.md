# 🎬 AI Video Editor - م<PERSON><PERSON><PERSON> الفيديو بالذكاء الاصطناعي

برنامج متقدم لتحرير الفيديو باستخدام الذكاء الاصطناعي مع ميزات:
- 🖼️ إزالة الخلفية تلقائياً
- 🎨 تحويل الأشخاص إلى شخصيات كرتونية/أنمي
- ✨ فلاتر احترافية متنوعة
- 👁️ معاينة مباشرة
- ⚡ معالجة سريعة ومتوازية

## 🚀 التشغيل السريع

### الطريقة الأولى: تشغيل مباشر

1. **تشغيل Backend:**
```bash
cd backend
pip install -r requirements.txt
python main.py
```

2. **تشغيل Frontend:**
```bash
cd frontend
npm install
npm run dev
```

3. **فتح التطبيق:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000

### الطريقة الثانية: Docker (قريباً)
```bash
docker-compose up
```

## 📋 المتطلبات

### Frontend
- Node.js 18+
- React 18 مع TypeScript
- Vite للتطوير السريع

### Backend
- Python 3.9+
- FastAPI للـ API
- OpenCV لمعالجة الصور
- NumPy للحوسبة العلمية

## ✨ الميزات المتاحة

### 🖼️ إزالة الخلفية
- خلفية شفافة
- خلفية بيضاء/سوداء
- شاشة خضراء/زرقاء
- خلفية متدرجة

### 🎨 تحويل الشخصيات
- تحويل إلى أنمي
- تحويل إلى كرتون
- رسم بالقلم الرصاص
- لوحة زيتية

### ✨ الفلاتر الاحترافية
- فينتاج (تأثير قديم)
- سايبربانك (مستقبلي)
- ألوان مائية
- نيون
- أبيض وأسود
- سيبيا
- ضبابي

## 🎯 كيفية الاستخدام

1. **رفع الفيديو:** اسحب ملف الفيديو إلى المنطقة المخصصة
2. **اختيار المعالجة:**
   - إزالة الخلفية
   - تحويل الشخصية
   - تطبيق فلتر
3. **المعاينة:** شاهد النتيجة مباشرة
4. **التحميل:** احفظ الفيديو المعدل

## 🔧 الإعدادات المتقدمة

### تخصيص شدة الفلاتر
يمكنك التحكم في شدة تأثير الفلاتر من 10% إلى 200%

### صيغ الفيديو المدعومة
- MP4, AVI, MOV, MKV, WMV, FLV
- الحد الأقصى: 100 ميجابايت

## 🏗️ البنية التقنية

```
📁 AI Video Editor/
├── 📁 frontend/           # واجهة React
│   ├── 📁 src/
│   │   ├── 📁 components/ # مكونات React
│   │   ├── App.tsx        # التطبيق الرئيسي
│   │   └── App.css        # التنسيقات
│   ├── package.json       # متطلبات Node.js
│   └── vite.config.ts     # إعدادات Vite
├── 📁 backend/            # خادم Python
│   ├── main.py            # خادم FastAPI
│   ├── video_processor.py # معالج الفيديو
│   ├── background_remover.py # إزالة الخلفية
│   ├── character_converter.py # تحويل الشخصيات
│   ├── ai_filters.py      # الفلاتر
│   └── requirements.txt   # متطلبات Python
├── docker-compose.yml     # إعدادات Docker
└── README.md             # هذا الملف
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في رفع الفيديو:** تأكد من صيغة الملف المدعومة
2. **بطء في المعالجة:** قلل من حجم الفيديو أو الدقة
3. **خطأ في الاتصال:** تأكد من تشغيل Backend على المنفذ 8000

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

---

**تم التطوير بـ ❤️ باستخدام React و FastAPI**
