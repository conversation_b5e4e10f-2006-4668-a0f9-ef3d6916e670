import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, Video, AlertCircle, CheckCircle } from 'lucide-react'
import axios from 'axios'

interface VideoFile {
  file_id: string
  filename: string
  file_path: string
  video_info: {
    width: number
    height: number
    fps: number
    duration: number
    file_size: number
  }
}

interface VideoUploaderProps {
  onVideoUploaded: (videoData: VideoFile) => void
}

const VideoUploader: React.FC<VideoUploaderProps> = ({ onVideoUploaded }) => {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    // التحقق من نوع الملف
    if (!file.type.startsWith('video/')) {
      setError('يرجى اختيار ملف فيديو صالح')
      return
    }

    // التحقق من حجم الملف (100MB كحد أقصى)
    if (file.size > 100 * 1024 * 1024) {
      setError('حجم الملف كبير جداً. الحد الأقصى 100 ميجابايت')
      return
    }

    setUploading(true)
    setError(null)
    setSuccess(false)
    setUploadProgress(0)

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await axios.post(`${API_BASE_URL}/upload-video`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            setUploadProgress(progress)
          }
        },
      })

      setSuccess(true)
      setUploadProgress(100)
      
      // تأخير قصير لإظهار رسالة النجاح
      setTimeout(() => {
        onVideoUploaded(response.data)
      }, 1000)

    } catch (err: any) {
      console.error('خطأ في رفع الفيديو:', err)
      setError(err.response?.data?.detail || 'حدث خطأ في رفع الفيديو')
    } finally {
      setUploading(false)
    }
  }, [API_BASE_URL, onVideoUploaded])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
    },
    multiple: false,
    disabled: uploading
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="video-uploader">
      <div className="upload-container">
        <div
          {...getRootProps()}
          className={`dropzone ${isDragActive ? 'active' : ''} ${uploading ? 'disabled' : ''}`}
        >
          <input {...getInputProps()} />
          
          <div className="dropzone-content">
            {uploading ? (
              <div className="upload-progress">
                <Video className="upload-icon spinning" size={48} />
                <h3>جاري رفع الفيديو...</h3>
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p>{uploadProgress}%</p>
              </div>
            ) : success ? (
              <div className="upload-success">
                <CheckCircle className="upload-icon success" size={48} />
                <h3>تم رفع الفيديو بنجاح!</h3>
                <p>جاري التحويل إلى محرر الفيديو...</p>
              </div>
            ) : (
              <div className="upload-prompt">
                <Upload className="upload-icon" size={48} />
                <h3>
                  {isDragActive 
                    ? 'اسحب الفيديو هنا...' 
                    : 'اسحب فيديو هنا أو انقر للاختيار'
                  }
                </h3>
                <p>الصيغ المدعومة: MP4, AVI, MOV, MKV, WMV, FLV</p>
                <p>الحد الأقصى: 100 ميجابايت</p>
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className="error-message">
            <AlertCircle size={20} />
            <span>{error}</span>
          </div>
        )}
      </div>

      <div className="features-preview">
        <h3>🚀 الميزات المتاحة</h3>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">🖼️</div>
            <h4>إزالة الخلفية</h4>
            <p>إزالة الخلفية تلقائياً باستخدام الذكاء الاصطناعي</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">🎨</div>
            <h4>تحويل إلى أنمي</h4>
            <p>تحويل الأشخاص إلى شخصيات أنمي وكرتون</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">✨</div>
            <h4>فلاتر احترافية</h4>
            <p>مجموعة متنوعة من الفلاتر والتأثيرات</p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">⚡</div>
            <h4>معالجة سريعة</h4>
            <p>معالجة متوازية للحصول على أفضل أداء</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VideoUploader
