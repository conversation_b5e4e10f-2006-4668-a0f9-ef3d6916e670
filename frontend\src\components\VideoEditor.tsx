import React, { useState, useRef } from 'react'
import { ArrowLeft, Download, Play, Pause, RotateCcw } from 'lucide-react'
import FilterPanel from './FilterPanel'
import PreviewPlayer from './PreviewPlayer'
import axios from 'axios'

interface VideoFile {
  file_id: string
  filename: string
  file_path: string
  video_info: {
    width: number
    height: number
    fps: number
    duration: number
    file_size: number
  }
}

interface VideoEditorProps {
  videoData: VideoFile
  onBackToUpload: () => void
}

interface ProcessingJob {
  type: 'background_removal' | 'character_conversion' | 'filter'
  status: 'idle' | 'processing' | 'completed' | 'error'
  progress: number
  result?: string
  error?: string
}

const VideoEditor: React.FC<VideoEditorProps> = ({ videoData, onBackToUpload }) => {
  const [currentJob, setCurrentJob] = useState<ProcessingJob | null>(null)
  const [processedVideos, setProcessedVideos] = useState<string[]>([])
  const [currentPreview, setCurrentPreview] = useState<string>(videoData.file_path)
  
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleRemoveBackground = async (backgroundType: string = 'transparent') => {
    setCurrentJob({
      type: 'background_removal',
      status: 'processing',
      progress: 0
    })

    try {
      const formData = new FormData()
      formData.append('file_id', videoData.file_id)
      formData.append('background_type', backgroundType)

      const response = await axios.post(`${API_BASE_URL}/remove-background`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })

      if (response.data.success) {
        const outputPath = response.data.output_path
        setProcessedVideos(prev => [...prev, outputPath])
        setCurrentPreview(outputPath)
        setCurrentJob({
          type: 'background_removal',
          status: 'completed',
          progress: 100,
          result: outputPath
        })
      }
    } catch (error: any) {
      console.error('خطأ في إزالة الخلفية:', error)
      setCurrentJob({
        type: 'background_removal',
        status: 'error',
        progress: 0,
        error: error.response?.data?.detail || 'حدث خطأ في إزالة الخلفية'
      })
    }
  }

  const handleCharacterConversion = async (style: string) => {
    setCurrentJob({
      type: 'character_conversion',
      status: 'processing',
      progress: 0
    })

    try {
      const formData = new FormData()
      formData.append('file_id', videoData.file_id)
      formData.append('style', style)

      const response = await axios.post(`${API_BASE_URL}/convert-character`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })

      if (response.data.success) {
        const outputPath = response.data.output_path
        setProcessedVideos(prev => [...prev, outputPath])
        setCurrentPreview(outputPath)
        setCurrentJob({
          type: 'character_conversion',
          status: 'completed',
          progress: 100,
          result: outputPath
        })
      }
    } catch (error: any) {
      console.error('خطأ في تحويل الشخصية:', error)
      setCurrentJob({
        type: 'character_conversion',
        status: 'error',
        progress: 0,
        error: error.response?.data?.detail || 'حدث خطأ في تحويل الشخصية'
      })
    }
  }

  const handleApplyFilter = async (filterName: string, intensity: number = 1.0) => {
    setCurrentJob({
      type: 'filter',
      status: 'processing',
      progress: 0
    })

    try {
      const formData = new FormData()
      formData.append('file_id', videoData.file_id)
      formData.append('filter_name', filterName)
      formData.append('intensity', intensity.toString())

      const response = await axios.post(`${API_BASE_URL}/apply-filter`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })

      if (response.data.success) {
        const outputPath = response.data.output_path
        setProcessedVideos(prev => [...prev, outputPath])
        setCurrentPreview(outputPath)
        setCurrentJob({
          type: 'filter',
          status: 'completed',
          progress: 100,
          result: outputPath
        })
      }
    } catch (error: any) {
      console.error('خطأ في تطبيق الفلتر:', error)
      setCurrentJob({
        type: 'filter',
        status: 'error',
        progress: 0,
        error: error.response?.data?.detail || 'حدث خطأ في تطبيق الفلتر'
      })
    }
  }

  const handleDownload = (filePath: string) => {
    const downloadUrl = `${API_BASE_URL}/download/${filePath.replace('outputs/', '')}`
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filePath.split('/').pop() || 'video.mp4'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const resetToOriginal = () => {
    setCurrentPreview(videoData.file_path)
    setCurrentJob(null)
  }

  return (
    <div className="video-editor">
      <div className="editor-header">
        <button className="back-button" onClick={onBackToUpload}>
          <ArrowLeft size={20} />
          العودة لرفع فيديو جديد
        </button>
        
        <div className="video-info">
          <h2>📹 {videoData.filename}</h2>
          <div className="info-details">
            <span>📐 {videoData.video_info.width}×{videoData.video_info.height}</span>
            <span>⏱️ {formatDuration(videoData.video_info.duration)}</span>
            <span>📊 {formatFileSize(videoData.video_info.file_size)}</span>
            <span>🎬 {videoData.video_info.fps.toFixed(1)} FPS</span>
          </div>
        </div>

        <div className="editor-actions">
          <button className="reset-button" onClick={resetToOriginal}>
            <RotateCcw size={16} />
            الأصلي
          </button>
          
          {currentPreview !== videoData.file_path && (
            <button 
              className="download-button"
              onClick={() => handleDownload(currentPreview)}
            >
              <Download size={16} />
              تحميل
            </button>
          )}
        </div>
      </div>

      <div className="editor-content">
        <div className="preview-section">
          <PreviewPlayer 
            videoSrc={`${API_BASE_URL}/${currentPreview}`}
            isProcessing={currentJob?.status === 'processing'}
          />
          
          {currentJob?.status === 'processing' && (
            <div className="processing-overlay">
              <div className="processing-content">
                <div className="spinner"></div>
                <h3>جاري المعالجة...</h3>
                <p>
                  {currentJob.type === 'background_removal' && 'إزالة الخلفية'}
                  {currentJob.type === 'character_conversion' && 'تحويل الشخصية'}
                  {currentJob.type === 'filter' && 'تطبيق الفلتر'}
                </p>
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${currentJob.progress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          {currentJob?.status === 'error' && (
            <div className="error-overlay">
              <div className="error-content">
                <h3>❌ حدث خطأ</h3>
                <p>{currentJob.error}</p>
                <button onClick={() => setCurrentJob(null)}>إغلاق</button>
              </div>
            </div>
          )}
        </div>

        <div className="controls-section">
          <FilterPanel
            onRemoveBackground={handleRemoveBackground}
            onCharacterConversion={handleCharacterConversion}
            onApplyFilter={handleApplyFilter}
            isProcessing={currentJob?.status === 'processing'}
          />
        </div>
      </div>

      {processedVideos.length > 0 && (
        <div className="processed-videos">
          <h3>📁 الفيديوهات المعالجة</h3>
          <div className="videos-list">
            {processedVideos.map((video, index) => (
              <div key={index} className="video-item">
                <span>{video.split('/').pop()}</span>
                <div className="video-actions">
                  <button onClick={() => setCurrentPreview(video)}>
                    معاينة
                  </button>
                  <button onClick={() => handleDownload(video)}>
                    <Download size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default VideoEditor
