import cv2
import numpy as np
import os
from typing import Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor
from video_processor import VideoProcessor

class BackgroundRemover:
    def __init__(self):
        # استخدام خوارزميات OpenCV التقليدية بدلاً من MediaPipe
        self.video_processor = VideoProcessor()
        self.executor = ThreadPoolExecutor(max_workers=4)

        # إعداد خوارزمية إزالة الخلفية
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=True, varThreshold=50
        )

    def create_background(self, width: int, height: int, bg_type: str = "transparent"):
        """إنشاء خلفية جديدة"""
        if bg_type == "transparent":
            # خلفية شفافة (سيتم حفظها كـ PNG)
            background = np.zeros((height, width, 4), dtype=np.uint8)
            background[:, :, 3] = 0  # قناة الشفافية
        elif bg_type == "white":
            background = np.ones((height, width, 3), dtype=np.uint8) * 255
        elif bg_type == "black":
            background = np.zeros((height, width, 3), dtype=np.uint8)
        elif bg_type == "green":
            background = np.zeros((height, width, 3), dtype=np.uint8)
            background[:, :, 1] = 255  # أخضر
        elif bg_type == "blue":
            background = np.zeros((height, width, 3), dtype=np.uint8)
            background[:, :, 0] = 255  # أزرق
        elif bg_type.startswith("gradient"):
            # خلفية متدرجة
            background = self.create_gradient_background(width, height)
        else:
            # خلفية بيضاء افتراضية
            background = np.ones((height, width, 3), dtype=np.uint8) * 255

        return background

    def create_gradient_background(self, width: int, height: int):
        """إنشاء خلفية متدرجة"""
        background = np.zeros((height, width, 3), dtype=np.uint8)

        # تدرج من الأزرق إلى الأرجواني
        for y in range(height):
            ratio = y / height
            # أزرق في الأعلى، أرجواني في الأسفل
            blue = int(255 * (1 - ratio))
            red = int(255 * ratio)
            background[y, :] = [blue, 0, red]

        return background

    def process_frame(self, frame: np.ndarray, background: np.ndarray, threshold: float = 0.5, frame_index: int = 0):
        """معالجة إطار واحد لإزالة الخلفية"""
        try:
            # استخدام خوارزمية إزالة الخلفية التقليدية
            if frame_index < 10:
                # في البداية، نحتاج لبناء نموذج الخلفية
                fg_mask = self.bg_subtractor.apply(frame)
            else:
                # بعد بناء النموذج، نحصل على القناع
                fg_mask = self.bg_subtractor.apply(frame, learningRate=0)

            # تحسين القناع
            # إزالة الضوضاء
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)

            # تطبيق تنعيم
            fg_mask = cv2.GaussianBlur(fg_mask, (5, 5), 0)

            # تحويل القناع إلى 3 أبعاد
            mask_3d = np.stack([fg_mask] * 3, axis=2) / 255.0

            # دمج الإطار مع الخلفية
            if background.shape[2] == 4:  # خلفية شفافة
                # إنشاء إطار RGBA
                frame_rgba = cv2.cvtColor(frame, cv2.COLOR_BGR2RGBA)
                frame_rgba[:, :, 3] = fg_mask
                result = frame_rgba
            else:
                # دمج عادي
                frame_float = frame.astype(np.float32)
                background_float = background.astype(np.float32)

                result = (frame_float * mask_3d +
                         background_float * (1 - mask_3d))
                result = result.astype(np.uint8)

            return result

        except Exception as e:
            print(f"خطأ في معالجة الإطار: {str(e)}")
            return frame

    def process_frame_batch(self, frames: list, background: np.ndarray, threshold: float = 0.5, start_index: int = 0):
        """معالجة مجموعة من الإطارات"""
        processed_frames = []
        for i, frame in enumerate(frames):
            processed_frame = self.process_frame(frame, background, threshold, start_index + i)
            processed_frames.append(processed_frame)
        return processed_frames

    async def remove_background(self, input_path: str, output_path: str,
                              background_type: str = "transparent",
                              threshold: float = 0.5):
        """إزالة الخلفية من الفيديو"""
        try:
            # فتح الفيديو
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise Exception("لا يمكن فتح الفيديو")

            # الحصول على خصائص الفيديو
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # إنشاء الخلفية
            background = self.create_background(width, height, background_type)

            # إعداد كاتب الفيديو
            if background_type == "transparent":
                # حفظ كـ PNG للشفافية (سيتم تحويله لاحقاً)
                temp_dir = "temp/frames_no_bg"
                os.makedirs(temp_dir, exist_ok=True)
                frame_paths = []
            else:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            # معالجة الإطارات
            frame_count = 0
            batch_size = 10
            frames_batch = []

            print(f"بدء معالجة {total_frames} إطار...")

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frames_batch.append(frame)

                # معالجة المجموعة عند امتلائها
                if len(frames_batch) >= batch_size:
                    # معالجة متوازية
                    loop = asyncio.get_event_loop()
                    processed_frames = await loop.run_in_executor(
                        self.executor,
                        self.process_frame_batch,
                        frames_batch,
                        background,
                        threshold,
                        frame_count
                    )

                    # حفظ الإطارات
                    for i, processed_frame in enumerate(processed_frames):
                        if background_type == "transparent":
                            frame_path = os.path.join(temp_dir, f"frame_{frame_count + i:06d}.png")
                            cv2.imwrite(frame_path, processed_frame)
                            frame_paths.append(frame_path)
                        else:
                            out.write(processed_frame)

                    frame_count += len(frames_batch)
                    frames_batch = []

                    # تقرير التقدم
                    progress = (frame_count / total_frames) * 100
                    print(f"التقدم: {progress:.1f}%")

            # معالجة الإطارات المتبقية
            if frames_batch:
                loop = asyncio.get_event_loop()
                processed_frames = await loop.run_in_executor(
                    self.executor,
                    self.process_frame_batch,
                    frames_batch,
                    background,
                    threshold,
                    frame_count
                )

                for i, processed_frame in enumerate(processed_frames):
                    if background_type == "transparent":
                        frame_path = os.path.join(temp_dir, f"frame_{frame_count + i:06d}.png")
                        cv2.imwrite(frame_path, processed_frame)
                        frame_paths.append(frame_path)
                    else:
                        out.write(processed_frame)

            cap.release()

            if background_type == "transparent":
                # تحويل إطارات PNG إلى فيديو مع الشفافية
                await self.create_transparent_video(frame_paths, output_path, fps)
                # تنظيف الملفات المؤقتة
                import shutil
                shutil.rmtree(temp_dir)
            else:
                out.release()

            # استخراج الصوت من الفيديو الأصلي وإضافته
            audio_path = "temp/audio.aac"
            try:
                self.video_processor.extract_audio(input_path, audio_path)
                if os.path.exists(audio_path):
                    temp_output = output_path.replace('.mp4', '_temp.mp4')
                    os.rename(output_path, temp_output)
                    self.video_processor.add_audio_to_video(temp_output, audio_path, output_path)
                    os.remove(temp_output)
                    os.remove(audio_path)
            except:
                pass  # إذا لم يكن هناك صوت، تجاهل الخطأ

            print("تم إزالة الخلفية بنجاح!")
            return {"success": True, "output_path": output_path}

        except Exception as e:
            raise Exception(f"خطأ في إزالة الخلفية: {str(e)}")

    async def create_transparent_video(self, frame_paths: list, output_path: str, fps: float):
        """إنشاء فيديو مع شفافية"""
        # هذه الوظيفة تحتاج FFmpeg مع دعم الشفافية
        # سنحفظ كـ WebM مع VP9 للشفافية
        import subprocess

        temp_list = "temp/frame_list.txt"
        with open(temp_list, 'w') as f:
            for frame_path in frame_paths:
                f.write(f"file '{os.path.abspath(frame_path)}'\n")
                f.write(f"duration {1/fps}\n")

        webm_output = output_path.replace('.mp4', '.webm')

        cmd = [
            'ffmpeg',
            '-f', 'concat',
            '-safe', '0',
            '-i', temp_list,
            '-c:v', 'libvpx-vp9',
            '-pix_fmt', 'yuva420p',
            '-y',
            webm_output
        ]

        try:
            subprocess.run(cmd, check=True, capture_output=True)
            # نسخ إلى MP4 أيضاً (بدون شفافية)
            subprocess.run([
                'ffmpeg', '-i', webm_output, '-c:v', 'libx264', '-y', output_path
            ], check=True, capture_output=True)
        except subprocess.CalledProcessError:
            # إذا فشل، احفظ كـ MP4 عادي
            self.video_processor.frames_to_video("temp/frames_no_bg", output_path, fps)

        # تنظيف
        if os.path.exists(temp_list):
            os.remove(temp_list)
