/* التنسيق العام للتطبيق */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.app-header h1 {
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.app-footer {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  margin-top: 2rem;
}

/* مكون رفع الفيديو */
.video-uploader {
  max-width: 800px;
  margin: 0 auto;
}

.upload-container {
  margin-bottom: 3rem;
}

.dropzone {
  border: 3px dashed rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.dropzone:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.dropzone.active {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.dropzone.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  color: rgba(255, 255, 255, 0.8);
}

.upload-icon.spinning {
  animation: spin 2s linear infinite;
}

.upload-icon.success {
  color: #4CAF50;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-prompt h3 {
  font-size: 1.5rem;
  margin: 0.5rem 0;
}

.upload-prompt p {
  margin: 0.25rem 0;
  opacity: 0.8;
}

.progress-bar {
  width: 100%;
  max-width: 300px;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  color: #ff6b6b;
}

/* معاينة الميزات */
.features-preview {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.features-preview h3 {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.feature-card h4 {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.feature-card p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

/* محرر الفيديو */
.video-editor {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.video-info h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.info-details {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.9rem;
  opacity: 0.8;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.reset-button, .download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.download-button {
  background: rgba(76, 175, 80, 0.2);
}

.reset-button:hover, .download-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.download-button:hover {
  background: rgba(76, 175, 80, 0.3);
}

.editor-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

@media (max-width: 768px) {
  .editor-content {
    grid-template-columns: 1fr;
  }
}

/* مشغل المعاينة */
.video-preview {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  overflow: hidden;
  position: relative;
}

.video-container {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-preview:hover .video-controls {
  opacity: 1;
}

.controls-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.time-display {
  color: white;
  font-size: 0.9rem;
}

.progress-container {
  position: relative;
  margin-bottom: 0.5rem;
}

.progress-slider {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.controls-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.video-info {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.info-item {
  display: flex;
  gap: 0.5rem;
}

.info-label {
  opacity: 0.7;
}

.info-value.processing {
  color: #ffa726;
}

.info-value.ready {
  color: #4CAF50;
}

/* لوحة الفلاتر */
.filter-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.panel-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 0.25rem;
}

.tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.panel-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.panel-content p {
  margin: 0 0 1.5rem 0;
  opacity: 0.8;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.option-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.option-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.option-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.option-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.option-label {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.option-description {
  font-size: 0.8rem;
  opacity: 0.8;
}

.intensity-control {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.intensity-control label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.intensity-control input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.processing-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  color: #ffc107;
}

/* طبقات المعالجة والأخطاء */
.processing-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.processing-content, .error-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  max-width: 300px;
}

.processing-content h3, .error-content h3 {
  margin: 1rem 0;
}

.error-content button {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

/* الفيديوهات المعالجة */
.processed-videos {
  margin-top: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
}

.processed-videos h3 {
  margin: 0 0 1rem 0;
}

.videos-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.video-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.video-actions {
  display: flex;
  gap: 0.5rem;
}

.video-actions button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.3s ease;
}

.video-actions button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .editor-header {
    flex-direction: column;
    align-items: stretch;
  }

  .info-details {
    justify-content: center;
  }

  .options-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .panel-tabs {
    flex-direction: column;
  }

  .tab {
    justify-content: flex-start;
  }
}
