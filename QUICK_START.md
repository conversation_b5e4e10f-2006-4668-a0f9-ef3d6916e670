# 🚀 دليل التشغيل السريع

## ✅ التطبيق جاهز للاستخدام!

### 🌐 الروابط:
- **التطبيق**: http://localhost:3000
- **API**: http://localhost:8000

### 🎬 كيفية الاستخدام:

#### 1️⃣ رفع الفيديو
- اسحب ملف فيديو إلى المنطقة المخصصة
- أو انقر لاختيار ملف
- الصيغ المدعومة: MP4, AVI, MOV, MKV, WMV, FLV
- الحد الأقصى: 100 ميجابايت

#### 2️⃣ اختيار نوع المعالجة

**🖼️ إزالة الخلفية:**
- شفاف
- أبيض/أسود
- أخضر/أزرق (شاشة خضراء)
- متدرج

**🎨 تحويل الشخصية:**
- أنمي
- كرتون
- رسم بالقلم
- لوحة زيتية

**✨ الفلاتر:**
- فينتاج
- سايبربانك
- ألوان مائية
- نيون
- أبيض وأسود
- سيبيا
- ضبابي

#### 3️⃣ المعاينة والتحميل
- شاهد النتيجة مباشرة
- تحكم في التشغيل (تشغيل/إيقاف/صوت)
- حمل الفيديو المعدل

### 🔧 إعادة التشغيل (إذا لزم الأمر):

```bash
# Backend
cd backend
python main.py

# Frontend (في terminal آخر)
cd frontend
npm run dev
```

### 🐛 حل المشاكل:

**إذا لم يعمل التطبيق:**
1. تأكد من تشغيل Backend على المنفذ 8000
2. تأكد من تشغيل Frontend على المنفذ 3000
3. تحديث الصفحة في المتصفح

**إذا كانت المعالجة بطيئة:**
- استخدم فيديو أصغر حجماً
- قلل من دقة الفيديو

### 📊 حالة الخوادم:
- ✅ Backend: يعمل على http://localhost:8000
- ✅ Frontend: يعمل على http://localhost:3000
- ✅ رفع الملفات: يعمل
- ✅ خدمة الملفات: يعمل

---

**🎉 استمتع بتحرير الفيديو بالذكاء الاصطناعي!**
