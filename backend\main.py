from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
import os
import shutil
import uuid
from pathlib import Path
import aiofiles
from typing import Optional
import json

from video_processor import VideoProcessor
from ai_filters import AIFilters
from background_remover import BackgroundRemover
from character_converter import CharacterConverter

app = FastAPI(title="AI Video Editor", version="1.0.0")

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إنشاء المجلدات المطلوبة
os.makedirs("uploads", exist_ok=True)
os.makedirs("outputs", exist_ok=True)
os.makedirs("temp", exist_ok=True)

# تهيئة المعالجات
video_processor = VideoProcessor()
ai_filters = AIFilters()
background_remover = BackgroundRemover()
character_converter = CharacterConverter()

# خدمة الملفات الثابتة
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

@app.get("/")
async def root():
    return {"message": "AI Video Editor API", "version": "1.0.0"}

@app.post("/upload-video")
async def upload_video(file: UploadFile = File(...)):
    """رفع ملف فيديو"""
    if not file.content_type.startswith('video/'):
        raise HTTPException(status_code=400, detail="يجب أن يكون الملف فيديو")

    # إنشاء معرف فريد للملف
    file_id = str(uuid.uuid4())
    file_extension = Path(file.filename).suffix
    file_path = f"uploads/{file_id}{file_extension}"

    # حفظ الملف
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    # الحصول على معلومات الفيديو
    video_info = video_processor.get_video_info(file_path)

    return {
        "file_id": file_id,
        "filename": file.filename,
        "file_path": file_path,
        "video_info": video_info
    }

@app.post("/remove-background")
async def remove_background(
    file_id: str = Form(...),
    background_type: str = Form(default="transparent")
):
    """إزالة خلفية الفيديو"""
    input_path = f"uploads/{file_id}.mp4"
    output_path = f"outputs/{file_id}_no_bg.mp4"

    if not os.path.exists(input_path):
        # البحث عن الملف بأي امتداد
        for ext in ['.mp4', '.avi', '.mov', '.mkv']:
            if os.path.exists(f"uploads/{file_id}{ext}"):
                input_path = f"uploads/{file_id}{ext}"
                break
        else:
            raise HTTPException(status_code=404, detail="الملف غير موجود")

    try:
        result = await background_remover.remove_background(
            input_path, output_path, background_type
        )
        return {
            "success": True,
            "output_path": output_path,
            "message": "تم إزالة الخلفية بنجاح"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إزالة الخلفية: {str(e)}")

@app.post("/convert-character")
async def convert_character(
    file_id: str = Form(...),
    style: str = Form(default="anime")  # anime, cartoon, sketch
):
    """تحويل الشخصية إلى أنمي أو كرتون"""
    input_path = f"uploads/{file_id}.mp4"
    output_path = f"outputs/{file_id}_{style}.mp4"

    if not os.path.exists(input_path):
        for ext in ['.mp4', '.avi', '.mov', '.mkv']:
            if os.path.exists(f"uploads/{file_id}{ext}"):
                input_path = f"uploads/{file_id}{ext}"
                break
        else:
            raise HTTPException(status_code=404, detail="الملف غير موجود")

    try:
        result = await character_converter.convert_style(
            input_path, output_path, style
        )
        return {
            "success": True,
            "output_path": output_path,
            "message": f"تم تحويل الشخصية إلى {style} بنجاح"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحويل الشخصية: {str(e)}")

@app.post("/apply-filter")
async def apply_filter(
    file_id: str = Form(...),
    filter_name: str = Form(...),
    intensity: float = Form(default=1.0)
):
    """تطبيق فلتر على الفيديو"""
    input_path = f"uploads/{file_id}.mp4"
    output_path = f"outputs/{file_id}_{filter_name}.mp4"

    if not os.path.exists(input_path):
        for ext in ['.mp4', '.avi', '.mov', '.mkv']:
            if os.path.exists(f"uploads/{file_id}{ext}"):
                input_path = f"uploads/{file_id}{ext}"
                break
        else:
            raise HTTPException(status_code=404, detail="الملف غير موجود")

    try:
        result = await ai_filters.apply_filter(
            input_path, output_path, filter_name, intensity
        )
        return {
            "success": True,
            "output_path": output_path,
            "message": f"تم تطبيق فلتر {filter_name} بنجاح"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تطبيق الفلتر: {str(e)}")

@app.get("/download/{file_path:path}")
async def download_file(file_path: str):
    """تحميل الملف المعالج"""
    full_path = f"outputs/{file_path}"
    if not os.path.exists(full_path):
        raise HTTPException(status_code=404, detail="الملف غير موجود")

    return FileResponse(
        full_path,
        media_type='application/octet-stream',
        filename=os.path.basename(full_path)
    )

@app.get("/filters")
async def get_available_filters():
    """الحصول على قائمة الفلاتر المتاحة"""
    return {
        "filters": [
            {"name": "vintage", "display_name": "فينتاج", "description": "تأثير قديم"},
            {"name": "cyberpunk", "display_name": "سايبربانك", "description": "تأثير مستقبلي"},
            {"name": "oil_painting", "display_name": "لوحة زيتية", "description": "تأثير لوحة فنية"},
            {"name": "watercolor", "display_name": "ألوان مائية", "description": "تأثير ألوان مائية"},
            {"name": "neon", "display_name": "نيون", "description": "تأثير أضواء نيون"},
            {"name": "black_white", "display_name": "أبيض وأسود", "description": "تحويل لأبيض وأسود"},
            {"name": "sepia", "display_name": "سيبيا", "description": "تأثير بني قديم"},
            {"name": "blur", "display_name": "ضبابي", "description": "تأثير ضبابي"},
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
