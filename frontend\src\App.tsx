import React, { useState } from 'react'
import VideoUploader from './components/VideoUploader'
import VideoEditor from './components/VideoEditor'
import './App.css'

interface VideoFile {
  file_id: string
  filename: string
  file_path: string
  video_info: {
    width: number
    height: number
    fps: number
    duration: number
    file_size: number
  }
}

function App() {
  const [uploadedVideo, setUploadedVideo] = useState<VideoFile | null>(null)
  const [currentStep, setCurrentStep] = useState<'upload' | 'edit'>('upload')

  const handleVideoUploaded = (videoData: VideoFile) => {
    setUploadedVideo(videoData)
    setCurrentStep('edit')
  }

  const handleBackToUpload = () => {
    setUploadedVideo(null)
    setCurrentStep('upload')
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🎬 محرر الفيديو بالذكاء الاصطناعي</h1>
        <p>إزالة الخلفية • تحويل إلى أنمي/كرتون • فلاتر احترافية</p>
      </header>

      <main className="app-main">
        {currentStep === 'upload' ? (
          <VideoUploader onVideoUploaded={handleVideoUploaded} />
        ) : (
          <VideoEditor
            videoData={uploadedVideo!}
            onBackToUpload={handleBackToUpload}
          />
        )}
      </main>

      <footer className="app-footer">
        <p>مدعوم بالذكاء الاصطناعي • تطوير محرر الفيديو المتقدم</p>
      </footer>
    </div>
  )
}

export default App
